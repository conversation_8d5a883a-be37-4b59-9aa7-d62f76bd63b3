import { Outlet, createFileRoute } from "@tanstack/react-router";
import Sidebar from "~/auth/components/Sidebar";
import Navbar from "~/modules/auth/components/Navbar";

export const Route = createFileRoute("/_authed/admin")({
	component: RouteComponent,
});

function RouteComponent() {
	return (
		<div className="flex h-screen w-full ">
			<Sidebar />
			<div className="flex w-full min-w-0 flex-col">
				<Navbar />
				<div className="flex-1 overflow-y-auto bg-base-200">
					<div className="container mx-auto px-4 py-8">
						<Outlet />
					</div>
				</div>
			</div>
		</div>
	);
}
